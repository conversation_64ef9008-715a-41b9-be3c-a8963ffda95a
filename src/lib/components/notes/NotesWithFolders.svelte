<script lang="ts">
	import { onMount, getContext } from 'svelte';
	import { toast } from 'svelte-sonner';
	import { goto } from '$app/navigation';
	import { WEBUI_NAME, user, config, models, settings } from '$lib/stores';

	import { createNewNote, getNotes } from '$lib/apis/notes';
	import {
		getNotesInFolder,
		getNotesInRoot,
		getNoteFolderPath,
		moveNoteToFolder
	} from '$lib/apis/note-folders';
	import { getTimeRange } from '$lib/utils';

	import { PaneGroup, Pane, PaneResizer } from 'paneforge';

	import FolderTree from './FolderTree.svelte';
	import Search from '../icons/Search.svelte';
	import Plus from '../icons/Plus.svelte';
	import XMark from '../icons/XMark.svelte';
	import ChevronRight from '../icons/ChevronRight.svelte';
	import Spinner from '../common/Spinner.svelte';
	import ChatBubbleOval from '../icons/ChatBubbleOval.svelte';
	import Tooltip from '../common/Tooltip.svelte';
	import NotePanel from './NotePanel.svelte';
	import Chat from './NoteEditor/Chat.svelte';

	const i18n = getContext('i18n');

	let selectedFolderId: string | null = null;
	let expandedFolders: Set<string> = new Set();
	let notes: any = {};
	let filteredNotes: any = {};
	let query = '';
	let loaded = false;
	let folderPath: any[] = [];

	// AI Chat 相關變數
	let showPanel = false;
	let selectedPanel = 'chat';
	let selectedModelId = null;
	let messages = [];
	let editing = false;
	let streaming = false;
	let stopResponseFlag = false;

	// 拖拽相關變數
	let draggedNote = null;
	let dragOverFolder = null;

	// 初始化
	const init = async () => {
		await loadNotes();
	};

	// 載入筆記
	const loadNotes = async () => {
		try {
			let notesList = [];

			if (selectedFolderId === null) {
				// 載入根目錄筆記
				notesList = await getNotesInRoot(localStorage.token);
				// 清空資料夾路徑
				folderPath = [];
			} else {
				// 載入指定資料夾筆記
				notesList = await getNotesInFolder(localStorage.token, selectedFolderId);
				// 載入資料夾路徑
				folderPath = await getNoteFolderPath(localStorage.token, selectedFolderId);
			}

			// 按時間分組
			const groupedNotes = {};
			notesList.forEach((note) => {
				const timeRange = getTimeRange(note.updated_at / 1000000000);
				if (!groupedNotes[timeRange]) {
					groupedNotes[timeRange] = [];
				}
				groupedNotes[timeRange].push(note);
			});

			notes = groupedNotes;
			applyFilter();
		} catch (error) {
			toast.error(`${error}`);
		}
	};

	// 應用搜索過濾
	const applyFilter = () => {
		if (!query.trim()) {
			filteredNotes = notes;
			return;
		}

		const filtered = {};
		Object.keys(notes).forEach((timeRange) => {
			const filteredTimeRangeNotes = notes[timeRange].filter(
				(note) =>
					note.title.toLowerCase().includes(query.toLowerCase()) ||
					(note.data?.content?.md || '').toLowerCase().includes(query.toLowerCase())
			);
			if (filteredTimeRangeNotes.length > 0) {
				filtered[timeRange] = filteredTimeRangeNotes;
			}
		});
		filteredNotes = filtered;
	};

	// 資料夾選擇處理
	const handleFolderSelected = async (event) => {
		selectedFolderId = event.detail.folderId;
		await loadNotes();
	};

	// 處理筆記移動事件
	const handleNotesMoved = async () => {
		await loadNotes();
	};

	// 創建新筆記
	const createNoteHandler = async () => {
		try {
			const res = await createNewNote(localStorage.token, {
				title: new Date().toISOString().split('T')[0], // YYYY-MM-DD
				data: {
					content: {
						json: null,
						html: '',
						md: ''
					}
				},
				meta: null,
				access_control: {},
				folder_id: selectedFolderId
			});

			if (res) {
				goto(`/notes/${res.id}`);
			}
		} catch (error) {
			toast.error(`${error}`);
		}
	};

	// 監聽搜索查詢變化
	$: if (query !== undefined) {
		applyFilter();
	}

	// 初始化模型選擇
	const initModel = () => {
		if ($settings?.models) {
			selectedModelId = $settings?.models[0];
		} else if ($config?.default_models) {
			selectedModelId = $config?.default_models.split(',')[0];
		} else {
			selectedModelId = '';
		}

		if (selectedModelId) {
			const model = $models
				.filter((model) => model.id === selectedModelId && !(model?.info?.meta?.hidden ?? false))
				.find((model) => model.id === selectedModelId);

			if (!model) {
				selectedModelId = '';
			}
		}

		if (!selectedModelId) {
			selectedModelId = $models.at(0)?.id || '';
		}
	};

	// 檢查筆記拖拽權限
	const canDragNote = (note) => {
		// 檢查用戶是否是筆記的擁有者
		if (note.user_id === $user.id) {
			return true;
		}

		// 檢查用戶是否有寫入權限
		if (note.access_control) {
			const writeAccess = note.access_control.write;
			if (writeAccess) {
				return writeAccess.user_ids?.includes($user.id) || false;
			}
		}

		return false;
	};

	// 拖拽處理函數
	const handleNoteDragStart = (event, note) => {
		// 檢查拖拽權限
		if (!canDragNote(note)) {
			event.preventDefault();
			toast.error($i18n.t('You do not have permission to move this note'));
			return;
		}

		draggedNote = note;
		event.dataTransfer.setData(
			'text/plain',
			JSON.stringify({
				type: 'note',
				id: note.id,
				title: note.title,
				currentFolderId: selectedFolderId,
				userId: note.user_id
			})
		);
		event.dataTransfer.effectAllowed = 'move';

		// 創建自定義拖拽圖像
		const dragImage = document.createElement('div');
		dragImage.innerHTML = `
			<div style="
				background: white;
				border: 2px solid #3b82f6;
				border-radius: 8px;
				padding: 8px 12px;
				box-shadow: 0 4px 12px rgba(0,0,0,0.15);
				font-size: 14px;
				color: #1f2937;
				max-width: 200px;
				white-space: nowrap;
				overflow: hidden;
				text-overflow: ellipsis;
			">
				📄 ${note.title}
			</div>
		`;
		dragImage.style.position = 'absolute';
		dragImage.style.top = '-1000px';
		document.body.appendChild(dragImage);
		event.dataTransfer.setDragImage(dragImage, 0, 0);

		// 延遲移除拖拽圖像
		setTimeout(() => {
			document.body.removeChild(dragImage);
		}, 0);

		// 添加拖拽樣式
		event.target.style.opacity = '0.5';
		event.target.style.transform = 'scale(0.95)';
	};

	const handleNoteDragEnd = (event) => {
		draggedNote = null;
		dragOverFolder = null;
		event.target.style.opacity = '1';
		event.target.style.transform = 'scale(1)';
	};

	const handleMoveNote = async (noteId, targetFolderId) => {
		try {
			await moveNoteToFolder(localStorage.token, noteId, targetFolderId);
			toast.success($i18n.t('Note moved successfully'));
			await loadNotes();
		} catch (error) {
			toast.error(`${error}`);
		}
	};

	// 創建虛擬筆記對象供 Chat 組件使用
	const createVirtualNote = () => {
		const notesList = Object.values(filteredNotes).flat();
		const notesContent = notesList
			.map((note) => `## ${note.title}\n${note.data?.content?.md || ''}`)
			.join('\n\n');

		return {
			id: 'virtual-notes',
			title: selectedFolderId ? `資料夾筆記` : '所有筆記',
			data: {
				content: {
					md: notesContent,
					html: '',
					json: null
				},
				files: null
			}
		};
	};

	onMount(async () => {
		await init();
		initModel();
		loaded = true;
	});
</script>

<svelte:head>
	<title>
		{$i18n.t('Notes')} • {$WEBUI_NAME}
	</title>
</svelte:head>

<div id="note-container" class="w-full h-full">
	<PaneGroup direction="horizontal" class="w-full h-full">
		<!-- 左側資料夾樹 -->
		<Pane defaultSize={30} minSize={15} maxSize={50} class="h-full">
			<div class="w-full h-full border-r border-gray-200 dark:border-gray-700">
				<FolderTree
					bind:selectedFolderId
					bind:expandedFolders
					on:folderSelected={handleFolderSelected}
					on:notesMoved={handleNotesMoved}
				/>
			</div>
		</Pane>

		<!-- 分隔線 -->
		<PaneResizer
			class="w-1 bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors cursor-col-resize"
		/>

		<!-- 右側筆記列表 -->
		<Pane defaultSize={70} minSize={30} class="h-full flex flex-col w-full relative">
			<div class="w-full h-full overflow-hidden">
				{#if loaded}
					<!-- 頭部工具欄 -->
					<div class="flex flex-col gap-1 px-4 py-3 border-b border-gray-200 dark:border-gray-700">
						<!-- 面包屑導航 -->
						{#if folderPath.length > 0}
							<div class="flex items-center text-sm text-gray-600 dark:text-gray-400 mb-2">
								<button
									type="button"
									class="hover:text-gray-900 dark:hover:text-gray-200"
									on:click={() => handleFolderSelected({ detail: { folderId: null } })}
								>
									{$i18n.t('Root')}
								</button>
								{#each folderPath as folder, index}
									<ChevronRight className="w-4 h-4 mx-1" />
									<button
										type="button"
										class="hover:text-gray-900 dark:hover:text-gray-200 {index ===
										folderPath.length - 1
											? 'font-medium text-gray-900 dark:text-gray-100'
											: ''}"
										on:click={() => handleFolderSelected({ detail: { folderId: folder.id } })}
									>
										{folder.name}
									</button>
								{/each}
							</div>
						{/if}

						<!-- 搜索和新建按鈕 -->
						<div class="flex items-center space-x-3">
							<div class="flex-1 flex items-center">
								<div class="mr-3">
									<Search className="w-4 h-4" />
								</div>
								<input
									class="w-full text-sm py-1 bg-transparent outline-none"
									bind:value={query}
									placeholder={$i18n.t('Search Notes')}
								/>
								{#if query}
									<button
										class="p-1 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700"
										on:click={() => {
											query = '';
										}}
									>
										<XMark className="w-3 h-3" />
									</button>
								{/if}
							</div>
							<Tooltip
								placement="top"
								content={$i18n.t('Chat with Notes')}
								className="cursor-pointer"
							>
								<button
									type="button"
									class="flex items-center px-3 py-1.5 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 rounded-md hover:bg-gray-200 dark:hover:bg-gray-600"
									on:click={() => {
										if (showPanel && selectedPanel === 'chat') {
											showPanel = false;
										} else {
											if (!showPanel) {
												showPanel = true;
											}
											selectedPanel = 'chat';
										}
									}}
								>
									<ChatBubbleOval className="w-4 h-4 mr-1" />
									{$i18n.t('Chat')}
								</button>
							</Tooltip>
							<button
								type="button"
								class="flex items-center px-3 py-1.5 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700"
								on:click={createNoteHandler}
							>
								<Plus className="w-4 h-4 mr-1" />
								{$i18n.t('New Note')}
							</button>
						</div>
					</div>

					<!-- 筆記列表 -->
					<div class="flex-1 overflow-y-auto p-4">
						{#if Object.keys(filteredNotes).length > 0}
							<div class="space-y-6">
								{#each Object.keys(filteredNotes) as timeRange}
									<div>
										<div class="text-xs text-gray-500 dark:text-gray-400 font-medium mb-3">
											{$i18n.t(timeRange)}
										</div>
										<div
											class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4"
										>
											{#each filteredNotes[timeRange] as note (note.id)}
												{@const isDraggable = canDragNote(note)}
												<div
													class="note-card bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer relative group {!isDraggable
														? 'opacity-75'
														: ''}"
													draggable={isDraggable}
													role="button"
													tabindex="0"
													aria-label="Drag note: {note.title}"
													on:dragstart={(e) => handleNoteDragStart(e, note)}
													on:dragend={handleNoteDragEnd}
												>
													<a href={`/notes/${note.id}`} class="block">
														<div class="flex items-start justify-between mb-2">
															<h3 class="font-medium text-gray-900 dark:text-white line-clamp-1">
																{note.title}
															</h3>
															<span class="text-xs text-gray-500 dark:text-gray-400 ml-2">
																{new Date(note.updated_at / 1000000).toLocaleDateString()}
															</span>
														</div>
														{#if note.data?.content?.md}
															<p class="text-sm text-gray-600 dark:text-gray-400 line-clamp-3">
																{note.data.content.md.substring(0, 150)}
																{note.data.content.md.length > 150 ? '...' : ''}
															</p>
														{/if}
													</a>
													<!-- 拖拽指示器 -->
													<div
														class="drag-indicator absolute top-2 right-2 opacity-0 transition-opacity pointer-events-none"
													>
														{#if isDraggable}
															<svg
																class="w-4 h-4 text-gray-400"
																fill="currentColor"
																viewBox="0 0 20 20"
															>
																<path
																	d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z"
																></path>
															</svg>
														{:else}
															<svg
																class="w-4 h-4 text-red-400"
																fill="currentColor"
																viewBox="0 0 20 20"
															>
																<path
																	fill-rule="evenodd"
																	d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z"
																	clip-rule="evenodd"
																></path>
															</svg>
														{/if}
													</div>
												</div>
											{/each}
										</div>
									</div>
								{/each}
							</div>
						{:else}
							<div class="flex flex-col items-center justify-center h-64 text-center">
								<div class="text-gray-500 dark:text-gray-400 mb-4">
									{#if query}
										{$i18n.t('No notes found matching your search')}
									{:else if selectedFolderId}
										{$i18n.t('No notes in this folder')}
									{:else}
										{$i18n.t('No notes yet')}
									{/if}
								</div>
								<button
									type="button"
									class="flex items-center px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700"
									on:click={createNoteHandler}
								>
									<Plus className="w-4 h-4 mr-2" />
									{$i18n.t('Create your first note')}
								</button>
							</div>
						{/if}
					</div>
				{:else}
					<div class="flex items-center justify-center h-full">
						<Spinner />
					</div>
				{/if}
			</div>
		</Pane>

		<!-- NotePanel for AI Chat -->
		<NotePanel bind:show={showPanel}>
			{#if selectedPanel === 'chat'}
				<Chat
					bind:show={showPanel}
					bind:selectedModelId
					bind:messages
					note={createVirtualNote()}
					bind:editing
					bind:streaming
					bind:stopResponseFlag
					editor={null}
					inputElement={null}
					selectedContent={null}
					files={[]}
					onInsert={() => {}}
					onStop={() => {
						stopResponseFlag = true;
					}}
					onEdited={() => {}}
					insertNoteHandler={() => {}}
					scrollToBottomHandler={() => {}}
				/>
			{/if}
		</NotePanel>
	</PaneGroup>
</div>

<style>
	.line-clamp-1 {
		display: -webkit-box;
		-webkit-line-clamp: 1;
		-webkit-box-orient: vertical;
		overflow: hidden;
	}

	.line-clamp-3 {
		display: -webkit-box;
		-webkit-line-clamp: 3;
		-webkit-box-orient: vertical;
		overflow: hidden;
	}

	/* 拖拽相關樣式 */
	.note-card {
		transition:
			opacity 0.2s ease,
			transform 0.2s ease;
	}

	.note-card:hover .drag-indicator {
		opacity: 0.5;
	}

	.note-card[draggable='true']:hover {
		cursor: grab;
	}

	.note-card[draggable='true']:active {
		cursor: grabbing;
	}

	/* 拖拽時的視覺反饋 */
	.dragging {
		opacity: 0.5;
		transform: scale(0.95);
		z-index: 1000;
	}
</style>
